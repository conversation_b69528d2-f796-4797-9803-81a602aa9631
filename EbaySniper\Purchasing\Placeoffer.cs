﻿using System.Data;
using DevExpress.XtraEditors;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;

namespace uBuyFirst.Purchasing
{
    public static class Placeoffer
    {
        public static bool PurchaseAllQuantity { get; set; }
        public static bool BestOfferSubtractShipping { get; set; }
        public static FormBid _dialogFormBid;

        public enum OrderAction
        {
            CommitToBuy,
            MakeOffer,
            PayWithCreditCard,
            BestOffer
        }

        public static void ShowPlaceOffer(DataRow? row, OrderAction orderAction, bool quickPurchase = false)
        {
            if (_dialogFormBid is { IsDisposed: true })
                return;

            if (row == null)
                return;

            var d = (DataList)row["Blob"];
            var commitToBuyAvailable = d.CommitToBuy && orderAction == OrderAction.CommitToBuy;
            var nonVariationItem = !d.Variation;
            var eBayAccountAdded = Form1.EBayAccountsList.Count > 0;
            // Create the appropriate order type based on the action
            if (orderAction == OrderAction.BestOffer)
            {
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                d.Order = new BuyingService.BestOfferOrder(d.ItemID, d.Title, d.EBaySite, PurchaseAllQuantity ? d.QuantityAvailable : 1, effectivePurchasePrice, effectivePurchasePrice.Value, effectivePurchasePrice.Currency);
            }
            else
            {
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                d.Order = new BuyingService.BuyOrder(d.ItemID, d.Title, d.EBaySite, PurchaseAllQuantity ? d.QuantityAvailable : 1, effectivePurchasePrice, orderAction);
            }
            var placeOfferEligible = ConnectionConfig.TradingAPIEnabled && nonVariationItem && Form1.LicenseUtility.CurrentLimits.PlaceOfferEnabled && eBayAccountAdded;
            if (placeOfferEligible)
            {
                if (d.Order.OrderAction is OrderAction.CommitToBuy or OrderAction.PayWithCreditCard)
                    if (!commitToBuyAvailable)
                    {
                        if (CreditCardService.CreditCardPaymentEnabled)
                        {
                            if (ConnectionConfig.CheckoutEnabled)
                            {
                                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Checkout started");
                                CreditCardCheckout.ExecuteCreditCardCheckout(d);
                                return;
                            }
                            else
                            {
                                Browser.OpenCheckoutLink(d);
                                return;
                            }

                        }
                        else
                        {

                            if (quickPurchase)
                            {
                                Browser.OpenCheckoutLink(d);
                            }
                            else
                            {
                                Browser.OpenAffiliateLink(d);
                            }

                            return;
                        }
                    }
                    else
                    {
                        if (UserSettings.SkipBuyConfirmation)
                        {
                            QuickPurchaseManager.QuickPurchase(FocusRouter.FocusedGridView, d);
                            return;
                        }
                    }

                _dialogFormBid ??= new FormBid();

                if (_dialogFormBid.Visible)
                    return;

                _dialogFormBid.SetupFormInfo(row);

                if (!_dialogFormBid.Visible)
                {
                    _dialogFormBid.ShowDialog();
                }

                return;
            }

            if (row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                return;

            if (d.ItemStatus == ItemStatus.Sold)
            {
                XtraMessageBox.Show(En_US.Form1_ShowPlaceOffer_Listing_is_not_available);
            }
            else
            {
                if (quickPurchase)
                {
                    Browser.OpenCheckoutLink(d);
                }
                else
                {
                    Browser.OpenAffiliateLink(d);
                }
            }
        }
    }
}

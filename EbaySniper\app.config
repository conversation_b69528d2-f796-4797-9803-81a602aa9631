﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="DevExpressXpoProfiler" type="DevExpress.Xpo.Logger.ProfilerConfigSection, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" allowLocation="true" allowDefinition="Everywhere" />
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection" requirePermission="false" />
    </sectionGroup>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value>Skin/DevExpress Style</value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>PerMonitorV2</value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value />
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <appSettings>
    <add key="EnableWindowsFormsHighDpiAutoResizing" value="true" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  <runtime>   
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">   
        <dependentAssembly>   
            <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-3.1113.0.0" newVersion="3.1113.0.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
        </dependentAssembly>   
        <dependentAssembly>   
            <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
        </dependentAssembly>   
    </assemblyBinding>
  </runtime>
  <!--  <system.diagnostics>
    <switches>
      <add name="XmlSerialization.PregenEventLog" value="1" />
    </switches>
  </system.diagnostics>
-->
  <DevExpressXpoProfiler serverType="DevExpress.Xpo.Logger.Transport.LogServer" serverAssembly="DevExpress.Xpo.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" categories="SQL;Session;DataCache" port="52934" />
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
      <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
    </providers>
  </entityFramework>
</configuration>
